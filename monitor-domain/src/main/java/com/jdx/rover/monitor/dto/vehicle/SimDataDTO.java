/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * SIM卡数据DTO
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
public class SimDataDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车号
     */
    private String vehicleName;

    /**
     * 制式
     * <p>例如：GSM, WCDMA, TD-SCDMA, LTE, 5G NR</p>
     */
    private String standard;

    /**
     * 频段
     * <p>例如：B1, B3, B5, B8, n41, n78等</p>
     */
    private String band;

    /**
     * 小区ID
     */
    private String communityId;

    /**
     * 基站ID
     */
    private String stationId;

    /**
     * RSRP (参考信号接收功率)
     * <p>单位：dBm，通常范围在-140到-40之间，值越大信号越强</p>
     */
    private Float rsrp;

    /**
     * SINR (信号与干扰加噪声比)
     * <p>单位：dB，通常范围在-20到30之间，值越大信号质量越好</p>
     */
    private Float sinr;

    /**
     * 日上传流量
     * <p>单位：MB</p>
     */
    private Double dailyUpload;

    /**
     * 日下载流量
     * <p>单位：MB</p>
     */
    private Double dailyDownload;

    /**
     * 设备唯一ID
     * <p>同类设备从1开始</p>
     */
    private Integer deviceId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;
}
