/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.dto.vehicle;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 硬件状态DTO
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@Data
public class HardwareStatusDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 车辆名称
     */
    private String vehicleName;

    /**
     * 域启动状态列表
     */
    private List<DomainBootDTO> domainBootList;

    /**
     * 激光雷达状态列表
     */
    private List<LidarStatusDTO> lidarStatusList;

    /**
     * SIM卡状态列表
     */
    private List<SimStatusDTO> simStatusList;

    /**
     * 域启动状态DTO
     */
    @Data
    public static class DomainBootDTO implements Serializable {
        private static final long serialVersionUID = 1L;
        // 根据实际需求添加字段
    }

    /**
     * 激光雷达状态DTO
     */
    @Data
    public static class LidarStatusDTO implements Serializable {
        private static final long serialVersionUID = 1L;
        // 根据实际需求添加字段
    }
}
