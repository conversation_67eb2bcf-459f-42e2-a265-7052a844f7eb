/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.server;

import cn.hutool.core.util.StrUtil;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.vehicle.SimDataDTO;
import com.jdx.rover.monitor.service.vehicle.VehicleSimInfoCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 接收车辆SIM卡数据信息
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerHardwareSimDataJmqConsumer implements MessageListener {

    /**
     * 车辆SIM卡信息缓存服务
     */
    private final VehicleSimInfoCacheService vehicleSimInfoCacheService;

    /**
     * 处理接收到的消息列表
     *
     * @param messages 消息列表，包含多个Message对象
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理SIM卡数据消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息
     *
     * @param message JSON格式的消息
     */
    public void handleOneMessage(String message) {
        try {
            // 解析消息为SimDataDTO列表
            List<SimDataDTO> simDataList = JsonUtils.readValue(message, 
                JsonUtils.getTypeFactory().constructCollectionType(List.class, SimDataDTO.class));
            
            if (simDataList == null || simDataList.isEmpty()) {
                log.warn("接收到空的SIM卡数据列表");
                return;
            }

            // 按车辆名称分组处理
            Map<String, List<SimDataDTO>> vehicleSimDataMap = simDataList.stream()
                .filter(simData -> StrUtil.isNotBlank(simData.getVehicleName()))
                .collect(Collectors.groupingBy(SimDataDTO::getVehicleName));

            // 分别缓存每个车辆的SIM卡数据
            for (Map.Entry<String, List<SimDataDTO>> entry : vehicleSimDataMap.entrySet()) {
                String vehicleName = entry.getKey();
                List<SimDataDTO> vehicleSimDataList = entry.getValue();
                
                log.info("处理车辆[{}]的SIM卡数据，数据条数：{}", vehicleName, vehicleSimDataList.size());
                vehicleSimInfoCacheService.cacheSimData(vehicleName, vehicleSimDataList);
            }

            log.info("成功处理SIM卡数据消息，涉及车辆数：{}，总数据条数：{}", 
                vehicleSimDataMap.size(), simDataList.size());

        } catch (Exception e) {
            log.error("解析SIM卡数据消息失败：{}", message, e);
            throw e;
        }
    }
}
