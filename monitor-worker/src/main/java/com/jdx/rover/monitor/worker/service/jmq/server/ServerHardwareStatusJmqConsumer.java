/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.worker.service.jmq.server;

import cn.hutool.core.util.StrUtil;
import com.jd.jmq.client.consumer.MessageListener;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.monitor.dto.vehicle.HardwareStatusDTO;
import com.jdx.rover.monitor.dto.vehicle.SimStatusDTO;
import com.jdx.rover.monitor.service.vehicle.VehicleSimInfoCacheService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 接收车辆硬件状态信息
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ServerHardwareStatusJmqConsumer implements MessageListener {

    /**
     * 车辆SIM卡信息缓存服务
     */
    private final VehicleSimInfoCacheService vehicleSimInfoCacheService;

    /**
     * 处理接收到的消息列表
     *
     * @param messages 消息列表，包含多个Message对象
     */
    @Override
    public void onMessage(List<Message> messages) throws Exception {
        for (Message message : messages) {
            log.info("Received topic={}, message={}", message.getTopic(), message.getText());
            if (StringUtils.isBlank(message.getText())) {
                continue;
            }
            try {
                handleOneMessage(message.getText());
            } catch (Exception e) {
                log.error("处理硬件状态消息失败!{}", message.getText(), e);
            }
        }
    }

    /**
     * 处理单个消息
     *
     * @param message JSON格式的消息
     */
    public void handleOneMessage(String message) {
        try {
            // 解析消息为HardwareStatusDTO
            HardwareStatusDTO hardwareStatus = JsonUtils.readValue(message, HardwareStatusDTO.class);
            
            if (hardwareStatus == null) {
                log.warn("接收到空的硬件状态数据");
                return;
            }

            String vehicleName = hardwareStatus.getVehicleName();
            if (StrUtil.isBlank(vehicleName)) {
                log.warn("硬件状态数据中车辆名称为空");
                return;
            }

            // 处理SIM卡状态信息
            List<SimStatusDTO> simStatusList = hardwareStatus.getSimStatusList();
            if (simStatusList != null && !simStatusList.isEmpty()) {
                log.info("处理车辆[{}]的SIM卡状态，状态条数：{}", vehicleName, simStatusList.size());
                vehicleSimInfoCacheService.cacheSimStatus(vehicleName, simStatusList);
            } else {
                log.debug("车辆[{}]的硬件状态中没有SIM卡状态信息", vehicleName);
            }

            log.info("成功处理车辆[{}]的硬件状态消息", vehicleName);

        } catch (Exception e) {
            log.error("解析硬件状态消息失败：{}", message, e);
            throw e;
        }
    }
}
