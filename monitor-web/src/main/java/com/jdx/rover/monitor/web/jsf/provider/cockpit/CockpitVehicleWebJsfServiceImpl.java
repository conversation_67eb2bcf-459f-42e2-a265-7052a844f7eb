/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 */
package com.jdx.rover.monitor.web.jsf.provider.cockpit;

import com.jdx.rover.common.domain.page.PageDTO;
import com.jdx.rover.common.utils.result.HttpResult;
import com.jdx.rover.jsf.annotation.ServiceInfo;
import com.jdx.rover.jsf.provider.AbstractProvider;
import com.jdx.rover.jsf.response.JsfResponse;
import com.jdx.rover.monitor.api.domain.dto.CockpitRealStatusDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitSingleVehicleDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitStationVehicleListDTO;
import com.jdx.rover.monitor.dto.cockpit.CockpitVehicleDTO;
import com.jdx.rover.monitor.dto.cockpit.HDMapInfoDTO;
import com.jdx.rover.monitor.service.cockpit.CockpitVehicleService;
import com.jdx.rover.monitor.vo.CockpitNumberVO;
import com.jdx.rover.monitor.vo.cockpit.BindVehicleVO;
import com.jdx.rover.monitor.vo.cockpit.CockpitSingleVehicleVO;
import com.jdx.rover.monitor.vo.cockpit.CockpitVehiclePageVO;
import com.jdx.rover.monitor.vo.cockpit.CockpitVehicleSearchVO;
import com.jdx.rover.monitor.vo.mapcollection.PositionVO;
import com.jdx.rover.monitor.web.jsf.api.cockpit.CockpitVehicleWebJsfService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 座席车辆
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class CockpitVehicleWebJsfServiceImpl extends AbstractProvider<CockpitVehicleWebJsfService> implements CockpitVehicleWebJsfService{

    /**
     * CockpitVehicleService
     */
    private final CockpitVehicleService cockpitVehicleService;

    @ServiceInfo(name = "获取车辆列表", webUrl = "/monitor/web/cockpit/getVehicleList")
    public HttpResult<PageDTO<CockpitVehicleDTO>> getVehicleList(@Valid CockpitVehiclePageVO cockpitVehiclePageVO) {
        PageDTO<CockpitVehicleDTO> pageDTO = cockpitVehicleService.getVehicleList(cockpitVehiclePageVO.getCockpitNumber(), cockpitVehiclePageVO.getPageNum(), cockpitVehiclePageVO.getPageSize());
        return HttpResult.success(pageDTO);
    }

    @ServiceInfo(name = "搜索车辆列表", webUrl = "/monitor/web/cockpit/searchVehicleList")
    public HttpResult<List<CockpitVehicleDTO>> searchVehicleList(@Valid CockpitVehicleSearchVO cockpitVehicleSearchVO) {
        List<CockpitVehicleDTO> result = cockpitVehicleService.searchVehicleList(cockpitVehicleSearchVO);
        return HttpResult.success(result);
    }

    @ServiceInfo(name = "座席绑定/解绑车辆", webUrl = "/monitor/web/cockpit/bindVehicle")
    public HttpResult<Void> bindVehicle(@Valid @RequestBody BindVehicleVO bindVehicleVO) {
        return cockpitVehicleService.bindVehicle(bindVehicleVO);
    }

    @ServiceInfo(name = "获取站点车辆列表", webUrl = "/monitor/web/cockpit/getStationVehicleList")
    public HttpResult<List<CockpitStationVehicleListDTO>> getStationVehicleList(CockpitNumberVO cockpitNumberVo) {
        List<CockpitStationVehicleListDTO> result = cockpitVehicleService.getStationVehicleList(cockpitNumberVo.getCockpitNumber());
        return HttpResult.success(result);
    }

    @ServiceInfo(name = "获取座席状态", webUrl = "/monitor/web/cockpit/getAllCockpitRealStatus")
    public HttpResult<List<CockpitRealStatusDTO>> getAllCockpitRealStatus() {
        List<CockpitRealStatusDTO> result = cockpitVehicleService.getAllCockpitRealStatus();
        return HttpResult.success(result);
    }

    @ServiceInfo(name = "获取坐席单车页信息", webUrl = "/monitor/web/cockpit/getSingleVehicle")
    public HttpResult<CockpitSingleVehicleDTO> getSingleVehicle(@Valid CockpitSingleVehicleVO cockpitSingleVehicleVO) {
        CockpitSingleVehicleDTO singleVehicle = cockpitVehicleService.getSingleVehicle(cockpitSingleVehicleVO);
        return HttpResult.success(singleVehicle);
    }

    @ServiceInfo(name = "根据经纬度获取高精地图信息", webUrl = "/monitor/web/cockpit/getMapInfoByPosition")
    public HttpResult<HDMapInfoDTO> getMapInfoByPosition(PositionVO positionVO) {
        return JsfResponse.response(() -> cockpitVehicleService.getMapInfoByPosition(positionVO));
    }
}