/*
 * Copyright (c) 2024 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */

package com.jdx.rover.monitor.service.vehicle;

import com.jdx.rover.monitor.dto.vehicle.SimDataDTO;
import com.jdx.rover.monitor.dto.vehicle.SimStatusDTO;
import com.jdx.rover.monitor.dto.vehicle.VehicleSimInfoDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 车辆SIM卡信息缓存服务
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class VehicleSimInfoCacheService {

    private final RedisTemplate<String, Object> redisTemplate;

    /**
     * SIM卡数据缓存key前缀
     */
    private static final String SIM_DATA_CACHE_KEY_PREFIX = "monitor:vehicle:sim:data:";

    /**
     * SIM卡状态缓存key前缀
     */
    private static final String SIM_STATUS_CACHE_KEY_PREFIX = "monitor:vehicle:sim:status:";

    /**
     * 缓存过期时间（小时）
     */
    private static final long CACHE_EXPIRE_HOURS = 24;

    /**
     * 缓存车辆SIM卡数据信息
     *
     * @param vehicleName 车辆名称
     * @param simDataList SIM卡数据列表
     */
    public void cacheSimData(String vehicleName, List<SimDataDTO> simDataList) {
        if (vehicleName == null || simDataList == null) {
            log.warn("缓存SIM卡数据失败：车辆名称或数据列表为空");
            return;
        }

        String cacheKey = SIM_DATA_CACHE_KEY_PREFIX + vehicleName;
        try {
            redisTemplate.opsForValue().set(cacheKey, simDataList, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            log.debug("成功缓存车辆[{}]的SIM卡数据，数据条数：{}", vehicleName, simDataList.size());
        } catch (Exception e) {
            log.error("缓存车辆[{}]的SIM卡数据失败", vehicleName, e);
        }
    }

    /**
     * 缓存车辆SIM卡状态信息
     *
     * @param vehicleName 车辆名称
     * @param simStatusList SIM卡状态列表
     */
    public void cacheSimStatus(String vehicleName, List<SimStatusDTO> simStatusList) {
        if (vehicleName == null || simStatusList == null) {
            log.warn("缓存SIM卡状态失败：车辆名称或状态列表为空");
            return;
        }

        String cacheKey = SIM_STATUS_CACHE_KEY_PREFIX + vehicleName;
        try {
            redisTemplate.opsForValue().set(cacheKey, simStatusList, CACHE_EXPIRE_HOURS, TimeUnit.HOURS);
            log.debug("成功缓存车辆[{}]的SIM卡状态，状态条数：{}", vehicleName, simStatusList.size());
        } catch (Exception e) {
            log.error("缓存车辆[{}]的SIM卡状态失败", vehicleName, e);
        }
    }

    /**
     * 获取车辆SIM卡数据信息
     *
     * @param vehicleName 车辆名称
     * @return SIM卡数据列表
     */
    @SuppressWarnings("unchecked")
    public List<SimDataDTO> getSimData(String vehicleName) {
        if (vehicleName == null) {
            log.warn("获取SIM卡数据失败：车辆名称为空");
            return null;
        }

        String cacheKey = SIM_DATA_CACHE_KEY_PREFIX + vehicleName;
        try {
            Object cachedData = redisTemplate.opsForValue().get(cacheKey);
            if (cachedData instanceof List) {
                return (List<SimDataDTO>) cachedData;
            }
            return null;
        } catch (Exception e) {
            log.error("获取车辆[{}]的SIM卡数据失败", vehicleName, e);
            return null;
        }
    }

    /**
     * 获取车辆SIM卡状态信息
     *
     * @param vehicleName 车辆名称
     * @return SIM卡状态列表
     */
    @SuppressWarnings("unchecked")
    public List<SimStatusDTO> getSimStatus(String vehicleName) {
        if (vehicleName == null) {
            log.warn("获取SIM卡状态失败：车辆名称为空");
            return null;
        }

        String cacheKey = SIM_STATUS_CACHE_KEY_PREFIX + vehicleName;
        try {
            Object cachedData = redisTemplate.opsForValue().get(cacheKey);
            if (cachedData instanceof List) {
                return (List<SimStatusDTO>) cachedData;
            }
            return null;
        } catch (Exception e) {
            log.error("获取车辆[{}]的SIM卡状态失败", vehicleName, e);
            return null;
        }
    }

    /**
     * 获取车辆完整的SIM卡信息
     *
     * @param vehicleName 车辆名称
     * @return 车辆SIM卡信息DTO
     */
    public VehicleSimInfoDTO getVehicleSimInfo(String vehicleName) {
        if (vehicleName == null) {
            log.warn("获取车辆SIM卡信息失败：车辆名称为空");
            return null;
        }

        VehicleSimInfoDTO vehicleSimInfo = new VehicleSimInfoDTO();
        vehicleSimInfo.setVehicleName(vehicleName);
        vehicleSimInfo.setSimDataList(getSimData(vehicleName));
        vehicleSimInfo.setSimStatusList(getSimStatus(vehicleName));

        return vehicleSimInfo;
    }

    /**
     * 删除车辆SIM卡缓存信息
     *
     * @param vehicleName 车辆名称
     */
    public void deleteVehicleSimCache(String vehicleName) {
        if (vehicleName == null) {
            log.warn("删除SIM卡缓存失败：车辆名称为空");
            return;
        }

        try {
            String simDataKey = SIM_DATA_CACHE_KEY_PREFIX + vehicleName;
            String simStatusKey = SIM_STATUS_CACHE_KEY_PREFIX + vehicleName;
            
            redisTemplate.delete(simDataKey);
            redisTemplate.delete(simStatusKey);
            
            log.debug("成功删除车辆[{}]的SIM卡缓存信息", vehicleName);
        } catch (Exception e) {
            log.error("删除车辆[{}]的SIM卡缓存信息失败", vehicleName, e);
        }
    }
}
